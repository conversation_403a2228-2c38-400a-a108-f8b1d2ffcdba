import { Course, Module, Lesson } from "@/types/course";
import J<PERSON><PERSON><PERSON> from "jszip";

export interface SCORMExportOptions {
  includeVideos?: boolean;
  packageTitle?: string;
  packageDescription?: string;
  version?: '1.2' | '2004';
}

class SCORMExporter {
  private generateManifest(course: Course, options: SCORMExportOptions = {}): string {
    const { version = '1.2' } = options;
    
    const manifestContent = `<?xml version="1.0" encoding="UTF-8"?>
<manifest identifier="course_${course.id}" version="1.0"
  xmlns="http://www.imsproject.org/xsd/imscp_rootv1p1p2"
  xmlns:adlcp="http://www.adlnet.org/xsd/adlcp_rootv1p2"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.imsproject.org/xsd/imscp_rootv1p1p2 imscp_rootv1p1p2.xsd
                      http://www.imsglobal.org/xsd/imsmd_rootv1p2p1 imsmd_rootv1p2p1.xsd
                      http://www.adlnet.org/xsd/adlcp_rootv1p2 adlcp_rootv1p2.xsd">

  <metadata>
    <schema>ADL SCORM</schema>
    <schemaversion>${version}</schemaversion>
    <lom xmlns="http://www.imsglobal.org/xsd/imsmd_rootv1p2p1">
      <general>
        <identifier>
          <catalog>URI</catalog>
          <entry>course_${course.id}</entry>
        </identifier>
        <title>
          <langstring xml:lang="en">${this.escapeXml(course.title)}</langstring>
        </title>
        <description>
          <langstring xml:lang="en">${this.escapeXml(course.description)}</langstring>
        </description>
      </general>
    </lom>
  </metadata>

  <organizations default="course_org">
    <organization identifier="course_org">
      <title>${this.escapeXml(course.title)}</title>
      ${course.modules.map((module, moduleIndex) => this.generateModuleXML(module, moduleIndex)).join('\n      ')}
    </organization>
  </organizations>

  <resources>
    <resource identifier="common_files" type="webcontent">
      <file href="styles/lesson.css"/>
      <file href="scripts/scorm-api.js"/>
    </resource>
    ${course.modules.map((module, moduleIndex) =>
      module.lessons.map((lesson, lessonIndex) =>
        this.generateResourceXML(lesson, moduleIndex, lessonIndex)
      ).join('\n    ')
    ).join('\n    ')}
  </resources>

</manifest>`;

    return manifestContent;
  }

  private generateModuleXML(module: Module, moduleIndex: number): string {
    return `<item identifier="module_${moduleIndex}" isvisible="true">
        <title>${this.escapeXml(module.title)}</title>
        ${module.lessons.map((lesson, lessonIndex) => 
          `<item identifier="lesson_${moduleIndex}_${lessonIndex}" isvisible="true" identifierref="resource_${moduleIndex}_${lessonIndex}">
            <title>${this.escapeXml(lesson.title)}</title>
          </item>`
        ).join('\n        ')}
      </item>`;
  }

  private generateResourceXML(lesson: Lesson, moduleIndex: number, lessonIndex: number): string {
    return `<resource identifier="resource_${moduleIndex}_${lessonIndex}" type="webcontent" adlcp:scormtype="sco" href="lessons/lesson_${moduleIndex}_${lessonIndex}.html">
      <file href="lessons/lesson_${moduleIndex}_${lessonIndex}.html"/>
      <dependency identifierref="common_files"/>
    </resource>`;
  }

  private generateLessonHTML(lesson: Lesson, moduleIndex: number, lessonIndex: number): string {
    const contentBlocks = lesson.contentBlocks || [];

    // Get video URL from metadata if available
    const videoUrl = lesson.metadata?.videoUrl || '';
    const videoEmbedUrl = this.convertToEmbedUrl(videoUrl);

    const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.escapeHtml(lesson.title)}</title>
    <link rel="stylesheet" href="../styles/lesson.css">
    <script src="../scripts/scorm-api.js"></script>
</head>
<body>
    <div class="lesson-container">
        <header class="lesson-header">
            <h1>${this.escapeHtml(lesson.title)}</h1>
            <div class="lesson-meta">
                <span class="duration">⏱️ Duration: ${lesson.duration}</span>
                <span class="type">📹 Type: ${lesson.type}</span>
            </div>
        </header>

        <main class="lesson-content">
            ${contentBlocks.length > 0 ?
              contentBlocks
                .sort((a, b) => (a.order || 0) - (b.order || 0))
                .map(block => this.generateContentBlockHTML(block))
                .join('\n            ')
              : this.generateFallbackContent(lesson, videoEmbedUrl)
            }
        </main>

        <footer class="lesson-footer">
            <div class="lesson-actions">
                <button onclick="completeLessonAndNext()" class="complete-btn">
                    ✅ Mark Complete & Continue
                </button>
                ${videoUrl ? `<a href="${videoUrl}" target="_blank" class="external-link-btn">
                    🔗 Open Video in New Tab
                </a>` : ''}
            </div>
        </footer>
    </div>

    <script>
        // SCORM API integration
        function completeLessonAndNext() {
            if (typeof scormAPI !== 'undefined') {
                scormAPI.setStatus('completed');
                scormAPI.setScore(100);
                scormAPI.commit();
            }

            // Show completion message
            const button = document.querySelector('.complete-btn');
            if (button) {
                button.innerHTML = '✅ Completed!';
                button.disabled = true;
                button.style.backgroundColor = '#10b981';
            }

            // Auto-advance after 2 seconds (optional)
            setTimeout(() => {
                if (window.parent && window.parent !== window) {
                    // Try to navigate to next lesson in LMS
                    window.parent.postMessage('lesson_completed', '*');
                }
            }, 2000);
        }

        // Initialize SCORM on page load
        window.onload = function() {
            if (typeof scormAPI !== 'undefined') {
                scormAPI.initialize();
                scormAPI.setStatus('incomplete');
            }

            // Add click tracking for video interactions
            const videos = document.querySelectorAll('iframe');
            videos.forEach(video => {
                video.addEventListener('load', () => {
                    console.log('Video loaded:', video.src);
                });
            });
        };

        // Handle video play events
        function trackVideoPlay(videoTitle) {
            if (typeof scormAPI !== 'undefined') {
                scormAPI.setStatus('incomplete');
                console.log('Video started:', videoTitle);
            }
        }

        // Handle quiz answers
        function checkQuizAnswer(quizName, correctAnswer) {
            const selected = document.querySelector('input[name="' + quizName + '"]:checked');
            const resultDiv = document.querySelector('.quiz-result') || document.createElement('div');
            resultDiv.className = 'quiz-result';

            if (!selected) {
                resultDiv.innerHTML = '<p class="quiz-feedback error">⚠️ Please select an answer first.</p>';
                document.querySelector('.content-quiz').appendChild(resultDiv);
                return;
            }

            const selectedValue = parseInt(selected.value);
            const isCorrect = selectedValue === correctAnswer;

            if (isCorrect) {
                resultDiv.innerHTML = '<p class="quiz-feedback success">✅ Correct! Well done.</p>';
                if (typeof scormAPI !== 'undefined') {
                    scormAPI.setScore(100);
                }
            } else {
                resultDiv.innerHTML = '<p class="quiz-feedback error">❌ Incorrect. Please review the content and try again.</p>';
                if (typeof scormAPI !== 'undefined') {
                    scormAPI.setScore(0);
                }
            }

            document.querySelector('.content-quiz').appendChild(resultDiv);

            // Disable quiz after answering
            const options = document.querySelectorAll('input[name="' + quizName + '"]');
            options.forEach(option => option.disabled = true);
            document.querySelector('.quiz-submit').disabled = true;
        }
    </script>
</body>
</html>`;

    return htmlContent;
  }

  private convertToEmbedUrl(url: string): string {
    if (!url) return '';

    // Convert YouTube watch URLs to embed URLs
    if (url.includes('youtube.com/watch?v=')) {
      const videoId = url.split('v=')[1]?.split('&')[0];
      return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
    }

    if (url.includes('youtu.be/')) {
      const videoId = url.split('youtu.be/')[1]?.split('?')[0];
      return videoId ? `https://www.youtube.com/embed/${videoId}` : url;
    }

    return url;
  }

  private generateFallbackContent(lesson: Lesson, videoEmbedUrl: string): string {
    return `
      <div class="lesson-intro">
        <h2>📚 ${this.escapeHtml(lesson.title)}</h2>
        <p class="lesson-description">${this.escapeHtml(lesson.content || 'No description available.')}</p>
      </div>

      ${videoEmbedUrl ? `
        <div class="content-video">
          <h3>🎥 Video Content</h3>
          <div class="video-container">
            <iframe
              src="${videoEmbedUrl}"
              frameborder="0"
              allowfullscreen
              title="${this.escapeHtml(lesson.title)}"
              onload="trackVideoPlay('${this.escapeHtml(lesson.title)}')"
            ></iframe>
          </div>
          <p class="video-duration">⏱️ Duration: ${lesson.duration}</p>
        </div>
      ` : ''}

      <div class="lesson-objectives">
        <h3>🎯 Learning Objectives</h3>
        <ul>
          <li>Understand the key concepts covered in this lesson</li>
          <li>Apply the knowledge to real-world scenarios</li>
          <li>Complete the lesson activities and assessments</li>
        </ul>
      </div>
    `;
  }

  private generateContentBlockHTML(block: any): string {
    switch (block.type) {
      case 'heading': {
        const level = block.content.level || 'h2';
        return `<${level} class="content-heading">${this.escapeHtml(block.content.text)}</${level}>`;
      }

      case 'text': {
        return `<div class="content-text"><p>${this.escapeHtml(block.content.text)}</p></div>`;
      }

      case 'video': {
        const videoUrl = this.convertToEmbedUrl(block.content.url || '');
        return `<div class="content-video">
          <h3>🎥 ${this.escapeHtml(block.content.title || 'Video Content')}</h3>
          <div class="video-container">
            <iframe
              src="${videoUrl}"
              frameborder="0"
              allowfullscreen
              title="${this.escapeHtml(block.content.title || 'Video')}"
              onload="trackVideoPlay('${this.escapeHtml(block.content.title || 'Video')}')"
            ></iframe>
          </div>
          ${block.content.duration ? `<p class="video-duration">⏱️ Duration: ${block.content.duration}</p>` : ''}
          <div class="video-description">
            <p>Watch this video to learn about the key concepts. You can pause and replay as needed.</p>
          </div>
        </div>`;
      }

      case 'quiz': {
        return `<div class="content-quiz">
          <h3>❓ ${this.escapeHtml(block.content.question)}</h3>
          <div class="quiz-options">
            ${block.content.options.map((option: string, index: number) =>
              `<label class="quiz-option">
                <input type="radio" name="quiz_${block.id}" value="${index}">
                ${this.escapeHtml(option)}
              </label>`
            ).join('')}
          </div>
          <button onclick="checkQuizAnswer('quiz_${block.id}', ${block.content.correctAnswer || 0})" class="quiz-submit">
            Submit Answer
          </button>
        </div>`;
      }

      case 'list': {
        const listType = block.content.listType === 'numbered' ? 'ol' : 'ul';
        return `<div class="content-list">
          <h3>📋 Key Points</h3>
          <${listType}>
            ${block.content.items.map((item: string) => `<li>${this.escapeHtml(item)}</li>`).join('')}
          </${listType}>
        </div>`;
      }

      case 'quote': {
        return `<blockquote class="content-quote">
          <p>"${this.escapeHtml(block.content.text)}"</p>
          ${block.content.author ? `<cite>— ${this.escapeHtml(block.content.author)}</cite>` : ''}
        </blockquote>`;
      }

      case 'divider': {
        return `<hr class="content-divider" />`;
      }

      default: {
        return `<div class="content-unknown">
          <p>⚠️ Content type "${block.type}" not supported in SCORM export</p>
          <details>
            <summary>Debug Info</summary>
            <pre>${JSON.stringify(block, null, 2)}</pre>
          </details>
        </div>`;
      }
    }
  }

  private generateCSS(): string {
    return `/* SCORM Lesson Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8fafc;
}

.lesson-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.lesson-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.lesson-header h1 {
    margin: 0 0 10px 0;
    font-size: 2rem;
}

.lesson-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.lesson-content {
    padding: 30px;
}

.content-heading {
    color: #2d3748;
    margin: 20px 0 15px 0;
}

.content-text {
    margin: 15px 0;
}

.content-video {
    margin: 20px 0;
    text-align: center;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    margin: 15px 0;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.content-quiz {
    background: #f7fafc;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.quiz-options {
    margin-top: 15px;
}

.quiz-option {
    display: block;
    margin: 10px 0;
    cursor: pointer;
}

.quiz-option input {
    margin-right: 10px;
}

.content-list ul, .content-list ol {
    margin: 15px 0;
    padding-left: 30px;
}

.content-quote {
    background: #edf2f7;
    border-left: 4px solid #667eea;
    padding: 20px;
    margin: 20px 0;
    font-style: italic;
}

.content-quote cite {
    display: block;
    margin-top: 10px;
    font-style: normal;
    font-weight: bold;
    color: #4a5568;
}

.lesson-footer {
    background: #f7fafc;
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
}

.lesson-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.complete-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 600;
}

.complete-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.complete-btn:disabled {
    background: #10b981;
    cursor: not-allowed;
    transform: none;
}

.external-link-btn {
    background: #f8fafc;
    color: #4a5568;
    border: 2px solid #e2e8f0;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.external-link-btn:hover {
    background: #e2e8f0;
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.video-description {
    margin-top: 15px;
    padding: 15px;
    background: #f0f9ff;
    border-left: 4px solid #3b82f6;
    border-radius: 4px;
}

.video-description p {
    margin: 0;
    color: #1e40af;
    font-style: italic;
}

.lesson-intro {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 25px;
    border-left: 4px solid #3b82f6;
}

.lesson-objectives {
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
    margin-top: 25px;
    border: 1px solid #e2e8f0;
}

.lesson-objectives h3 {
    margin-top: 0;
    color: #2d3748;
}

.lesson-objectives ul {
    margin-bottom: 0;
}

.lesson-objectives li {
    margin-bottom: 8px;
    color: #4a5568;
}

.quiz-submit {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    margin-top: 15px;
    transition: background 0.2s;
}

.quiz-submit:hover {
    background: #2563eb;
}

.quiz-submit:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.quiz-result {
    margin-top: 15px;
    padding: 15px;
    border-radius: 6px;
}

.quiz-feedback.success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
    margin: 0;
    padding: 10px;
    border-radius: 4px;
}

.quiz-feedback.error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
    margin: 0;
    padding: 10px;
    border-radius: 4px;
}

.content-divider {
    border: none;
    height: 2px;
    background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
    margin: 30px 0;
}

.content-unknown {
    background: #fed7d7;
    color: #c53030;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}`;
  }

  private generateSCORMAPI(): string {
    return `// Basic SCORM API implementation
var scormAPI = {
    initialized: false,
    
    initialize: function() {
        this.initialized = true;
        console.log('SCORM API initialized');
        return 'true';
    },
    
    setStatus: function(status) {
        if (!this.initialized) return 'false';
        localStorage.setItem('scorm_status', status);
        console.log('SCORM status set to:', status);
        return 'true';
    },
    
    getStatus: function() {
        if (!this.initialized) return 'false';
        return localStorage.getItem('scorm_status') || 'not attempted';
    },
    
    setScore: function(score) {
        if (!this.initialized) return 'false';
        localStorage.setItem('scorm_score', score.toString());
        console.log('SCORM score set to:', score);
        return 'true';
    },
    
    getScore: function() {
        if (!this.initialized) return '0';
        return localStorage.getItem('scorm_score') || '0';
    },
    
    commit: function() {
        if (!this.initialized) return 'false';
        console.log('SCORM data committed');
        return 'true';
    },
    
    terminate: function() {
        this.initialized = false;
        console.log('SCORM API terminated');
        return 'true';
    }
};

// Make API available globally
window.scormAPI = scormAPI;`;
  }

  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  async exportCourse(course: Course, options: SCORMExportOptions = {}): Promise<Blob> {
    const zip = new JSZip();
    
    // Add manifest file
    zip.file('imsmanifest.xml', this.generateManifest(course, options));
    
    // Add lesson HTML files
    const lessonsFolder = zip.folder('lessons');
    course.modules.forEach((module, moduleIndex) => {
      module.lessons.forEach((lesson, lessonIndex) => {
        const lessonHTML = this.generateLessonHTML(lesson, moduleIndex, lessonIndex);
        lessonsFolder?.file(`lesson_${moduleIndex}_${lessonIndex}.html`, lessonHTML);
      });
    });
    
    // Add CSS and JavaScript files
    const stylesFolder = zip.folder('styles');
    stylesFolder?.file('lesson.css', this.generateCSS());
    
    const scriptsFolder = zip.folder('scripts');
    scriptsFolder?.file('scorm-api.js', this.generateSCORMAPI());
    
    // Generate and return the zip file
    return await zip.generateAsync({ type: 'blob' });
  }
}

export const scormExporter = new SCORMExporter();

export const downloadSCORMPackage = async (course: Course, options: SCORMExportOptions = {}) => {
  try {
    const blob = await scormExporter.exportCourse(course, options);
    
    // Create download link
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${course.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_scorm.zip`;
    
    // Trigger download
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up
    URL.revokeObjectURL(url);
    
    return true;
  } catch (error) {
    console.error('Error exporting SCORM package:', error);
    throw new Error('Failed to export SCORM package');
  }
};
