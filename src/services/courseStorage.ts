import { Course } from "@/types/course";

export interface StoredCourse extends Course {
  status: 'draft' | 'in-progress' | 'published';
  lastModified: string;
  createdAt: string;
}

export interface CourseStorageService {
  saveDraft: (course: Course) => Promise<string>;
  getDrafts: () => Promise<StoredCourse[]>;
  getDraft: (id: string) => Promise<StoredCourse | null>;
  updateDraft: (id: string, course: Course) => Promise<void>;
  deleteDraft: (id: string) => Promise<void>;
  publishCourse: (id: string) => Promise<void>;
  getPublishedCourses: () => Promise<StoredCourse[]>;
}

class LocalStorageCourseService implements CourseStorageService {
  private readonly STORAGE_KEY = 'learnify_courses';

  private getCourses(): StoredCourse[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error reading courses from localStorage:', error);
      return [];
    }
  }

  private saveCourses(courses: StoredCourse[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(courses));
    } catch (error) {
      console.error('Error saving courses to localStorage:', error);
      throw new Error('Failed to save course');
    }
  }

  async saveDraft(course: Course): Promise<string> {
    const courses = this.getCourses();
    const now = new Date().toISOString();
    
    const storedCourse: StoredCourse = {
      ...course,
      status: 'draft',
      lastModified: now,
      createdAt: course.metadata?.createdAt || now,
      metadata: {
        ...course.metadata,
        updatedAt: now
      }
    };

    // Check if course already exists (update) or create new
    const existingIndex = courses.findIndex(c => c.id === course.id);
    if (existingIndex >= 0) {
      courses[existingIndex] = storedCourse;
    } else {
      courses.push(storedCourse);
    }

    this.saveCourses(courses);
    return course.id;
  }

  async getDrafts(): Promise<StoredCourse[]> {
    const courses = this.getCourses();
    return courses.filter(course => course.status === 'draft');
  }

  async getDraft(id: string): Promise<StoredCourse | null> {
    const courses = this.getCourses();
    return courses.find(course => course.id === id) || null;
  }

  async updateDraft(id: string, course: Course): Promise<void> {
    const courses = this.getCourses();
    const index = courses.findIndex(c => c.id === id);
    
    if (index === -1) {
      throw new Error('Course not found');
    }

    const now = new Date().toISOString();
    courses[index] = {
      ...course,
      status: courses[index].status, // Preserve existing status
      lastModified: now,
      createdAt: courses[index].createdAt, // Preserve creation date
      metadata: {
        ...course.metadata,
        updatedAt: now
      }
    };

    this.saveCourses(courses);
  }

  async deleteDraft(id: string): Promise<void> {
    const courses = this.getCourses();
    const filteredCourses = courses.filter(course => course.id !== id);
    this.saveCourses(filteredCourses);
  }

  async publishCourse(id: string): Promise<void> {
    const courses = this.getCourses();
    const course = courses.find(c => c.id === id);
    
    if (!course) {
      throw new Error('Course not found');
    }

    course.status = 'published';
    course.lastModified = new Date().toISOString();
    
    this.saveCourses(courses);
  }

  async getPublishedCourses(): Promise<StoredCourse[]> {
    const courses = this.getCourses();
    return courses.filter(course => course.status === 'published');
  }
}

// Export singleton instance
export const courseStorage = new LocalStorageCourseService();

// Utility functions for course management
export const formatLastModified = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  } else if (diffInHours < 48) {
    return 'Yesterday';
  } else {
    const days = Math.floor(diffInHours / 24);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  }
};

export const getStatusColor = (status: StoredCourse['status']): string => {
  switch (status) {
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'in-progress':
      return 'bg-blue-100 text-blue-800';
    case 'published':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getStatusIcon = (status: StoredCourse['status']): string => {
  switch (status) {
    case 'draft':
      return '📝';
    case 'in-progress':
      return '⚡';
    case 'published':
      return '✅';
    default:
      return '📝';
  }
};
