
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Plus, BookOpen, Users, BarChart3, Settings, Search, Bell, User, ArrowRight, Sparkles, FileText, Edit3 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/sonner";
import CourseBuilder from "@/components/CourseBuilder";
import AnalyticsDashboard from "@/components/AnalyticsDashboard";
import CollaborationPanel from "@/components/CollaborationPanel";
import { Course } from "@/types/course";

const Index = () => {
  const [activeView, setActiveView] = useState("dashboard");
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [coursePrompt, setCoursePrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);

  const recentCourses = [
    {
      id: 1,
      title: "Introduction to Digital Marketing",
      description: "Comprehensive course covering digital marketing fundamentals",
      progress: 75,
      status: "In Progress",
      collaborators: 3,
      lastModified: "2 hours ago",
      modules: 8,
      completionRate: 89
    },
    {
      id: 2,
      title: "Advanced JavaScript Concepts",
      description: "Deep dive into modern JavaScript programming",
      progress: 45,
      status: "Draft",
      collaborators: 2,
      lastModified: "1 day ago",
      modules: 12,
      completionRate: 0
    },
    {
      id: 3,
      title: "Project Management Essentials",
      description: "Essential skills for effective project management",
      progress: 100,
      status: "Published",
      collaborators: 5,
      lastModified: "3 days ago",
      modules: 6,
      completionRate: 94
    }
  ];

  const generateCourseFromPrompt = async (prompt: string) => {
    setIsGenerating(true);
    try {
      const response = await fetch('https://tkhqppfqsitovjvsstfl.supabase.co/functions/v1/learning_path_generator', {
        method: 'POST',
        headers: {
          'sec-ch-ua-platform': '"macOS"',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IlI0MDZ2NGl1NlMrZncwTk4iLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YJqQRqGeD_UqIJEu6AWPmchYkT8BUDzkNb1CotXScKI',
          'Referer': 'https://admin-dev.netskill.com/',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
          'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'Content-Type': 'application/json; charset=utf-8',
          'sec-ch-ua-mobile': '?0'
        },
        body: JSON.stringify({
          query: prompt,
          designation: "mba",
          format: "youtube videos",
          language: "English"
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform the API response into a Course object
      const generatedCourse = transformApiResponseToCourse(data, prompt);

      // Pass the generated course to handleCreateNew
      handleCreateNew(generatedCourse);

      toast.success("Course generated successfully!");

    } catch (error) {
      console.error('Error generating course:', error);
      toast.error("Failed to generate course. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const transformApiResponseToCourse = (apiData: any, originalPrompt: string): Course => {
    // Transform the API response into the expected Course structure
    // This is a basic transformation - you may need to adjust based on actual API response structure
    const modules = apiData.modules || apiData.learning_path || [];

    return {
      id: `course-${Date.now()}`,
      title: apiData.title || `Course: ${originalPrompt}`,
      description: apiData.description || `AI-generated course based on: ${originalPrompt}`,
      modules: modules.map((module: any, index: number) => ({
        id: `module-${index + 1}`,
        title: module.title || module.name || `Module ${index + 1}`,
        description: module.description || module.summary || "",
        order: index,
        status: "draft" as const,
        estimatedDuration: module.duration || "30 min",
        lessons: (module.lessons || module.topics || []).map((lesson: any, lessonIndex: number) => ({
          id: `lesson-${index + 1}-${lessonIndex + 1}`,
          title: lesson.title || lesson.name || `Lesson ${lessonIndex + 1}`,
          type: "text" as const,
          duration: lesson.duration || "15 min",
          content: lesson.content || lesson.description || "",
          status: "draft" as const,
          order: lessonIndex
        }))
      })),
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: "1.0.0",
        author: "AI Course Generator",
        tags: apiData.tags || [],
        difficulty: apiData.difficulty || "beginner",
        estimatedDuration: apiData.estimatedDuration || "2 hours",
        language: "English"
      },
      settings: {
        visibility: "private",
        pricing: "free",
        completionRequirements: "all-modules",
        adaptiveLearning: "disabled"
      }
    };
  };

  const handleCreateNew = (generatedCourse?: Course) => {
    setActiveView("builder");
    setSelectedCourse(generatedCourse || null);
  };

  const handlePromptSubmit = () => {
    if (!coursePrompt.trim()) {
      toast.error("Please enter a course idea first.");
      return;
    }
    generateCourseFromPrompt(coursePrompt.trim());
  };

  const handleEditCourse = (course) => {
    setSelectedCourse(course);
    setActiveView("builder");
  };

  if (activeView === "builder") {
    return <CourseBuilder course={selectedCourse} onBack={() => setActiveView("dashboard")} />;
  }

  if (activeView === "analytics") {
    return <AnalyticsDashboard onBack={() => setActiveView("dashboard")} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* AI-Powered Course Builder Interface */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        {/* Greeting */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Hi, Ishika!
          </h1>
        </div>

        {/* Main Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="w-8 h-8 text-purple-600 mr-3" />
            <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              AI-Powered Course Builder
            </h2>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Just describe your course idea in a few words - our AI will instantly generate a complete
            course with structured modules, lessons, and learning outcomes.
          </p>
        </div>

        {/* Option Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 hover:border-purple-200">
            <CardContent className="p-8">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Use a Template</h3>
                </div>
                <ArrowRight className="w-5 h-5 text-gray-400 ml-auto group-hover:text-purple-600 transition-colors" />
              </div>
              <p className="text-gray-600">
                Choose a template, then tailor it to your content.
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 hover:border-blue-200">
            <CardContent className="p-8">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                  <Edit3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Start from Scratch</h3>
                </div>
                <ArrowRight className="w-5 h-5 text-gray-400 ml-auto group-hover:text-blue-600 transition-colors" />
              </div>
              <p className="text-gray-600">
                Build your course from scratch with full control.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* AI Prompt Input */}
        <div className="mb-8">
          <Card className="border-2 border-dashed border-gray-200 hover:border-purple-300 transition-colors">
            <CardContent className="p-8">
              <div className="relative">
                <div className="flex items-center mb-4">
                  <Sparkles className="w-5 h-5 text-purple-600 mr-2" />
                  <span className="text-sm font-medium text-gray-700">AI Course Generator</span>
                </div>
                <Textarea
                  value={coursePrompt}
                  onChange={(e) => setCoursePrompt(e.target.value)}
                  placeholder="Type your course idea — we'll take it from here."
                  className="min-h-[120px] text-lg border-0 shadow-none resize-none focus-visible:ring-0 placeholder:text-gray-400"
                  disabled={isGenerating}
                />
                <div className="flex justify-end mt-4">
                  <Button
                    onClick={handlePromptSubmit}
                    disabled={isGenerating || !coursePrompt.trim()}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-full"
                  >
                    {isGenerating ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Generating...
                      </div>
                    ) : (
                      <ArrowRight className="w-5 h-5" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer Text */}
        <div className="text-center">
          <p className="text-sm text-gray-500">
            NETSKILL AI is powerful but still evolving — be sure to review and edit as needed.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
