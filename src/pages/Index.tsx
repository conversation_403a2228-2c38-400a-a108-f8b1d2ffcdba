
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Sparkles, FileText, Edit3, BookOpen } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/sonner";
import CourseBuilder from "@/components/CourseBuilder";
import AnalyticsDashboard from "@/components/AnalyticsDashboard";
import DraftCourseCard from "@/components/DraftCourseCard";
import { Course } from "@/types/course";
import { courseStorage, StoredCourse } from "@/services/courseStorage";

const Index = () => {
  const [activeView, setActiveView] = useState("dashboard");
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [coursePrompt, setCoursePrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [draftCourses, setDraftCourses] = useState<StoredCourse[]>([]);
  const [isLoadingDrafts, setIsLoadingDrafts] = useState(true);

  // Load draft courses on component mount
  useEffect(() => {
    loadDraftCourses();
  }, []);

  const loadDraftCourses = async () => {
    try {
      setIsLoadingDrafts(true);
      const drafts = await courseStorage.getDrafts();
      setDraftCourses(drafts);
    } catch (error) {
      console.error('Error loading draft courses:', error);
      toast.error("Failed to load draft courses");
    } finally {
      setIsLoadingDrafts(false);
    }
  };

  const handleEditDraftCourse = (course: StoredCourse) => {
    setSelectedCourse(course);
    setActiveView("builder");
  };

  const handleDeleteDraftCourse = (courseId: string) => {
    setDraftCourses(prev => prev.filter(course => course.id !== courseId));
  };

  const handlePublishDraftCourse = (courseId: string) => {
    setDraftCourses(prev => prev.filter(course => course.id !== courseId));
  };

  const generateCourseFromPrompt = async (prompt: string) => {
    setIsGenerating(true);
    try {
      const response = await fetch('https://tkhqppfqsitovjvsstfl.supabase.co/functions/v1/learning_path_generator', {
        method: 'POST',
        headers: {
          'sec-ch-ua-platform': '"macOS"',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6IlI0MDZ2NGl1NlMrZncwTk4iLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YJqQRqGeD_UqIJEu6AWPmchYkT8BUDzkNb1CotXScKI',
          'Referer': 'https://admin-dev.netskill.com/',
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
          'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'Content-Type': 'application/json; charset=utf-8',
          'sec-ch-ua-mobile': '?0'
        },
        body: JSON.stringify({
          query: prompt,
          designation: "mba",
          format: "youtube videos",
          language: "English"
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data);

      // Transform the API response into a Course object
      const generatedCourse = transformApiResponseToCourse(data, prompt);
      console.log('Generated Course:', generatedCourse);

      // Pass the generated course to handleCreateNew
      handleCreateNew(generatedCourse);

      toast.success("Course generated successfully!");

    } catch (error) {
      console.error('Error generating course:', error);

      // For demo purposes, if API fails, create a sample course
      if (error instanceof Error && error.message.includes('CORS')) {
        toast.error("CORS error - using demo data instead");
        const demoData = {
          title: "MBA Course Learning Path",
          img_url: "https://img.freepik.com/free-photo/business-growth-white-drawing_1134-99.jpg",
          journey: {
            "Introduction to MBA": [
              {
                title: "What is an MBA Degree?",
                link: "https://www.youtube.com/watch?v=H2M9c3seREk",
                type: "video",
                duration: "9:18",
                img_url: "https://img.freepik.com/free-photo/business-growth-white-drawing_1134-99.jpg"
              },
              {
                title: "Benefits of Doing an MBA",
                link: "https://www.youtube.com/watch?v=vrlutHYlIDg",
                duration: "3:00",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/growth_152105-874.jpg"
              }
            ],
            "Marketing Management": [
              {
                title: "Principles of Marketing",
                link: "https://www.youtube.com/watch?v=sR-qL7QdVZQ",
                duration: "57:30",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/marketing_91719-98.jpg"
              },
              {
                title: "Market Research Basics",
                link: "https://www.youtube.com/watch?v=GHqGPFxfOLA",
                duration: "1:12:46",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/research_290298-122.jpg"
              }
            ],
            "Operational Management": [
              {
                title: "Introduction to Operations Management",
                link: "https://www.youtube.com/watch?v=l-MnHBREzG8",
                duration: "11:48",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/management_163109-208.jpg"
              },
              {
                title: "Supply Chain Management Basics",
                link: "https://www.youtube.com/watch?v=Lpp9bHtPAN0",
                duration: "6:36",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/supply_443107-345.jpg"
              }
            ],
            "Financial Management": [
              {
                title: "Introduction to Financial Management",
                link: "https://www.youtube.com/watch?v=MKLd1iw1lFw",
                duration: "12:04",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/finance_87545-90.jpg"
              },
              {
                title: "Understanding Financial Statements",
                link: "https://www.youtube.com/watch?v=Fi1wkUczuyk",
                duration: "9:06",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/statements_281981-133.jpg"
              }
            ],
            "Human Resource Management": [
              {
                title: "Functions of HR Management",
                link: "https://www.youtube.com/watch?v=bI9RZjF-538",
                duration: "10:57",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/hrm_28198-202.jpg"
              },
              {
                title: "Recruitment & Selection Process",
                link: "https://www.youtube.com/watch?v=hHXlsJ2VQ70",
                duration: "8:07",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/recruitment_20147-203.jpg"
              }
            ],
            "Strategic Management": [
              {
                title: "Introduction to Strategic Management",
                link: "https://www.youtube.com/watch?v=5xD2JLleGqk",
                duration: "8:26",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/strategy_173109-401.jpg"
              },
              {
                title: "Tools for Strategic Analysis",
                link: "https://www.youtube.com/watch?v=bYn4CyL3r5w",
                duration: "9:49",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/analysis_321038-229.jpg"
              }
            ],
            "Business Analytics": [
              {
                title: "Basics of Business Analytics",
                link: "https://www.youtube.com/watch?v=diaZdX1s5L4",
                duration: "11:33",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/analytics_92754-321.jpg"
              },
              {
                title: "Data Visualization Techniques",
                link: "https://www.youtube.com/watch?v=MiiANxRHSv4",
                duration: "27:21",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/visualization_178091-122.jpg"
              }
            ],
            "Entrepreneurship": [
              {
                title: "Starting a New Business",
                link: "https://www.youtube.com/watch?v=JJZQT1t2_n8",
                duration: "19:16",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/business_293981-193.jpg"
              },
              {
                title: "Challenges in Entrepreneurship",
                link: "https://www.youtube.com/watch?v=oMk9Qh3x6Js",
                duration: "1:46",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/challenge_090189-221.jpg"
              }
            ],
            "Leadership & Management": [
              {
                title: "Effective Leadership Skills",
                link: "https://www.youtube.com/watch?v=bk4ERJ3MkCE",
                duration: "8:56",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/leadership_10281-302.jpg"
              },
              {
                title: "Team Dynamics and Management",
                link: "https://www.youtube.com/watch?v=FVoxjstO5A4",
                duration: "6:27",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/dynamics_183019-12.jpg"
              }
            ],
            "Project Management": [
              {
                title: "Introduction to Project Management",
                link: "https://www.youtube.com/watch?v=f0kUfDLXqPE",
                duration: "45:45",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/project_131905-202.jpg"
              },
              {
                title: "Tools for Project Management",
                link: "https://www.youtube.com/watch?v=RmKIRxtBInA",
                duration: "14:16",
                type: "video",
                img_url: "https://img.freepik.com/free-photo/tools_47395-200.jpg"
              }
            ]
          }
        };
        const generatedCourse = transformApiResponseToCourse(demoData, coursePrompt);
        handleCreateNew(generatedCourse);
        return;
      }

      toast.error("Failed to generate course. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  const transformApiResponseToCourse = (apiData: any, originalPrompt: string): Course => {
    // Transform the API response into the expected Course structure
    // The API returns a structure with title, img_url, and journey object
    const journey = apiData.journey || {};

    // Convert journey object to modules array
    const modules = Object.entries(journey).map(([moduleTitle, lessons], index) => {
      const moduleId = `module-${index + 1}`;

      // Calculate total duration for the module
      const totalDuration = (lessons as any[]).reduce((total, lesson) => {
        const duration = lesson.duration || "0:00";
        const [minutes, seconds] = duration.split(':').map(Number);
        return total + (minutes || 0) + ((seconds || 0) / 60);
      }, 0);

      return {
        id: moduleId,
        title: moduleTitle,
        description: `Learn about ${moduleTitle.toLowerCase()} through curated video content`,
        order: index,
        status: index === 0 ? "in-progress" as const : "draft" as const,
        estimatedDuration: `${Math.ceil(totalDuration)} min`,
        lessons: (lessons as any[]).map((lesson: any, lessonIndex: number) => ({
          id: `lesson-${index + 1}-${lessonIndex + 1}`,
          title: lesson.title,
          type: lesson.type === "video" ? "video" as const : "text" as const,
          duration: lesson.duration || "15 min",
          content: `Watch this video to learn about ${lesson.title}`,
          status: index === 0 && lessonIndex === 0 ? "in-progress" as const : "draft" as const,
          order: lessonIndex,
          contentBlocks: [
            {
              id: `${moduleId}-lesson-${lessonIndex + 1}-heading`,
              type: 'heading' as const,
              order: 0,
              content: { text: lesson.title, level: 'h2' }
            },
            {
              id: `${moduleId}-lesson-${lessonIndex + 1}-video`,
              type: 'video' as const,
              order: 1,
              content: {
                url: lesson.link,
                title: lesson.title,
                duration: lesson.duration,
                thumbnail: lesson.img_url
              }
            },
            {
              id: `${moduleId}-lesson-${lessonIndex + 1}-description`,
              type: 'text' as const,
              order: 2,
              content: {
                text: `This ${lesson.duration} video covers key concepts in ${lesson.title}. Take notes and pause as needed to fully understand the material.`
              }
            }
          ],
          metadata: {
            videoUrl: lesson.link,
            imageUrls: [lesson.img_url],
            duration: lesson.duration
          }
        }))
      };
    });

    return {
      id: `course-${Date.now()}`,
      title: apiData.title || `Course: ${originalPrompt}`,
      description: `AI-generated ${apiData.title || 'course'} based on: ${originalPrompt}`,
      modules,
      metadata: {
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        version: "1.0.0",
        author: "AI Course Generator",
        tags: ["AI Generated", "Video Course", originalPrompt],
        difficulty: "beginner" as const,
        estimatedDuration: `${modules.reduce((total, module) => {
          const duration = parseInt(module.estimatedDuration) || 0;
          return total + duration;
        }, 0)} min`,
        language: "English"
      },
      settings: {
        visibility: "private",
        pricing: "free",
        completionRequirements: "all-modules",
        adaptiveLearning: "disabled"
      }
    };
  };

  const handleCreateNew = (generatedCourse?: Course) => {
    setActiveView("builder");
    setSelectedCourse(generatedCourse || null);
  };

  const handleBackFromBuilder = () => {
    setActiveView("dashboard");
    setSelectedCourse(null);
    // Refresh draft courses when returning from builder
    loadDraftCourses();
  };

  const handlePromptSubmit = () => {
    if (!coursePrompt.trim()) {
      toast.error("Please enter a course idea first.");
      return;
    }
    generateCourseFromPrompt(coursePrompt.trim());
  };



  if (activeView === "builder") {
    return <CourseBuilder course={selectedCourse} onBack={handleBackFromBuilder} />;
  }

  if (activeView === "analytics") {
    return <AnalyticsDashboard onBack={() => setActiveView("dashboard")} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-blue-50">
      {/* AI-Powered Course Builder Interface */}
      <div className="max-w-4xl mx-auto px-6 py-16">
        {/* Greeting */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Hi, Ishika!
          </h1>
        </div>

        {/* Main Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="w-8 h-8 text-purple-600 mr-3" />
            <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              AI-Powered Course Builder
            </h2>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Just describe your course idea in a few words - our AI will instantly generate a complete
            course with structured modules, lessons, and learning outcomes.
          </p>
        </div>

        {/* Option Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 hover:border-purple-200">
            <CardContent className="p-8">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Use a Template</h3>
                </div>
                <ArrowRight className="w-5 h-5 text-gray-400 ml-auto group-hover:text-purple-600 transition-colors" />
              </div>
              <p className="text-gray-600">
                Choose a template, then tailor it to your content.
              </p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border-2 hover:border-blue-200">
            <CardContent className="p-8">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                  <Edit3 className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">Start from Scratch</h3>
                </div>
                <ArrowRight className="w-5 h-5 text-gray-400 ml-auto group-hover:text-blue-600 transition-colors" />
              </div>
              <p className="text-gray-600">
                Build your course from scratch with full control.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* AI Prompt Input */}
        <div className="mb-8">
          <Card className="border-2 border-dashed border-gray-200 hover:border-purple-300 transition-colors">
            <CardContent className="p-8">
              <div className="relative">
                <div className="flex items-center mb-4">
                  <Sparkles className="w-5 h-5 text-purple-600 mr-2" />
                  <span className="text-sm font-medium text-gray-700">AI Course Generator</span>
                </div>
                <Textarea
                  value={coursePrompt}
                  onChange={(e) => setCoursePrompt(e.target.value)}
                  placeholder="Type your course idea — we'll take it from here."
                  className="min-h-[120px] text-lg border-0 shadow-none resize-none focus-visible:ring-0 placeholder:text-gray-400"
                  disabled={isGenerating}
                />
                <div className="flex justify-end mt-4">
                  <Button
                    onClick={handlePromptSubmit}
                    disabled={isGenerating || !coursePrompt.trim()}
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3 rounded-full"
                  >
                    {isGenerating ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Generating...
                      </div>
                    ) : (
                      <ArrowRight className="w-5 h-5" />
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Draft Courses Section */}
        {draftCourses.length > 0 && (
          <div className="mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BookOpen className="w-5 h-5 mr-2" />
                  My Draft Courses
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoadingDrafts ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                    <p className="text-gray-600 mt-2">Loading draft courses...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {draftCourses.map((course) => (
                      <DraftCourseCard
                        key={course.id}
                        course={course}
                        onEdit={handleEditDraftCourse}
                        onDelete={handleDeleteDraftCourse}
                        onPublish={handlePublishDraftCourse}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Footer Text */}
        <div className="text-center">
          <p className="text-sm text-gray-500">
            NETSKILL AI is powerful but still evolving — be sure to review and edit as needed.
          </p>
        </div>
      </div>
    </div>
  );
};

export default Index;
