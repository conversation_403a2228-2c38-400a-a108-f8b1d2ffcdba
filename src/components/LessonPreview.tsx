import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ContentBlockRenderer } from "@/components/ContentBlockLibrary";
import { 
  ArrowLeft, 
  Edit, 
  Clock, 
  User, 
  BookOpen,
  CheckCircle,
  Circle
} from "lucide-react";
import { Lesson, ContentBlock } from "@/types/course";

interface LessonPreviewProps {
  lesson: Lesson;
  moduleId: string;
  onBack: () => void;
  onEdit: () => void;
}

const LessonPreview = ({ lesson, moduleId, onBack, onEdit }: LessonPreviewProps) => {
  const [completedBlocks, setCompletedBlocks] = useState<Set<string>>(new Set());

  const toggleBlockCompletion = (blockId: string) => {
    const newCompleted = new Set(completedBlocks);
    if (newCompleted.has(blockId)) {
      newCompleted.delete(blockId);
    } else {
      newCompleted.add(blockId);
    }
    setCompletedBlocks(newCompleted);
  };

  const progressPercentage = lesson.contentBlocks?.length 
    ? Math.round((completedBlocks.size / lesson.contentBlocks.length) * 100)
    : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="p-2">
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {lesson.title}
                </h1>
                <p className="text-sm text-gray-600">Lesson Preview</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={onEdit}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Lesson
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Lesson Header */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="capitalize">
                    {lesson.type}
                  </Badge>
                  <Badge variant="outline" className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{lesson.duration}</span>
                  </Badge>
                  <Badge 
                    variant="outline" 
                    className={
                      lesson.status === 'completed' ? 'bg-green-100 text-green-800 border-green-200' :
                      lesson.status === 'in-progress' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                      'bg-gray-100 text-gray-800 border-gray-200'
                    }
                  >
                    {lesson.status}
                  </Badge>
                </div>
                <CardTitle className="text-2xl">{lesson.title}</CardTitle>
                {lesson.description && (
                  <p className="text-gray-600">{lesson.description}</p>
                )}
              </div>
              
              <div className="text-right">
                <div className="text-2xl font-bold text-blue-600">{progressPercentage}%</div>
                <p className="text-sm text-gray-500">Complete</p>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Lesson Content */}
        <div className="space-y-6">
          {lesson.contentBlocks && lesson.contentBlocks.length > 0 ? (
            lesson.contentBlocks
              .sort((a, b) => a.order - b.order)
              .map((block, index) => (
                <Card key={block.id} className="group">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0 mt-1">
                        <button
                          onClick={() => toggleBlockCompletion(block.id)}
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                        >
                          {completedBlocks.has(block.id) ? (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          ) : (
                            <Circle className="w-5 h-5" />
                          )}
                        </button>
                      </div>
                      
                      <div className="flex-1">
                        <ContentBlockRenderer
                          block={block}
                          isEditing={false}
                          tempContent={block.content}
                          onContentChange={() => {}}
                          onSave={() => {}}
                          onCancel={() => {}}
                        />
                      </div>
                      
                      <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Badge variant="outline" className="text-xs">
                          {index + 1}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <BookOpen className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Content Yet</h3>
                <p className="text-gray-600 mb-4">This lesson doesn't have any content blocks yet.</p>
                <Button onClick={onEdit}>
                  <Edit className="w-4 h-4 mr-2" />
                  Add Content
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Lesson Footer */}
        {lesson.contentBlocks && lesson.contentBlocks.length > 0 && (
          <Card className="mt-8">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">
                      {completedBlocks.size} of {lesson.contentBlocks.length} sections completed
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {progressPercentage === 100 ? (
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Completed
                    </Badge>
                  ) : (
                    <Badge variant="outline">
                      In Progress
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="mt-4">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default LessonPreview;
