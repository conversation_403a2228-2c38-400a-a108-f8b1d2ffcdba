import { useState, useRef } from "react";
import { Dnd<PERSON><PERSON><PERSON>, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ContentBlockRenderer } from "@/components/ContentBlockLibrary";
import LessonPreview from "@/components/LessonPreview";
import {
  ArrowLeft,
  Save,
  Eye,
  Plus,
  GripVertical,
  Type,
  Heading,
  Image,
  Video,
  Mic,
  HelpCircle,
  Minus,
  MousePointer,
  Code,
  List,
  Quote,
  Globe,
  Trash2,
  <PERSON>ting<PERSON>,
  <PERSON>gn<PERSON><PERSON><PERSON>,
  AlignCenter,
  AlignRight
} from "lucide-react";
import { Lesson, ContentBlock, DragItem } from "@/types/course";

interface LessonEditorProps {
  lesson: Lesson;
  moduleId: string;
  onBack: () => void;
  onSave: (updatedLesson: Lesson) => void;
}

interface ContentToolProps {
  type: string;
  label: string;
  icon: React.ReactNode;
  description: string;
}

const ContentTool = ({ type, label, icon, description }: ContentToolProps) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'content-tool',
    item: { type: 'content-tool', contentType: type, label },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className={`p-3 border rounded-lg cursor-grab hover:shadow-sm transition-all ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      <div className="flex items-center space-x-3">
        <div className="text-blue-600">{icon}</div>
        <div className="flex-1">
          <h4 className="font-medium text-sm">{label}</h4>
          <p className="text-xs text-gray-500">{description}</p>
        </div>
      </div>
    </div>
  );
};

interface ContentBlockComponentProps {
  block: ContentBlock;
  index: number;
  onMove: (dragIndex: number, hoverIndex: number) => void;
  onUpdate: (blockId: string, content: any) => void;
  onDelete: (blockId: string) => void;
}

const ContentBlockComponent = ({
  block,
  index,
  onMove,
  onUpdate,
  onDelete
}: ContentBlockComponentProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [tempContent, setTempContent] = useState(block.content || {});

  const [{ handlerId }, drop] = useDrop({
    accept: 'content-block',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) return;

      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset!.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

      onMove(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: 'content-block',
    item: () => ({ id: block.id, index, type: 'content-block' } as DragItem),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(drop(ref));

  const handleSave = () => {
    onUpdate(block.id, tempContent);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTempContent(block.content || {});
    setIsEditing(false);
  };

  return (
    <Card
      ref={ref}
      className={`mb-4 group hover:shadow-md transition-all ${
        isDragging ? 'opacity-50' : ''
      }`}
      data-handler-id={handlerId}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity">
            <GripVertical className="h-4 w-4 text-gray-400 mt-1" />
          </div>

          <div className="flex-1" onClick={() => !isEditing && setIsEditing(true)}>
            <ContentBlockRenderer
              block={block}
              isEditing={isEditing}
              tempContent={tempContent}
              onContentChange={setTempContent}
              onSave={handleSave}
              onCancel={handleCancel}
            />
          </div>

          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            {!isEditing && (
              <>
                <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                  <Settings className="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => onDelete(block.id)}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface DropZoneProps {
  onDrop: (item: any) => void;
  children: React.ReactNode;
}

const DropZone = ({ onDrop, children }: DropZoneProps) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'content-tool',
    drop: (item) => onDrop(item),
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  return (
    <div
      ref={drop}
      className={`min-h-[100px] border-2 border-dashed rounded-lg transition-colors ${
        isOver ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
      }`}
    >
      {children}
    </div>
  );
};

const LessonEditor = ({ lesson, moduleId, onBack, onSave }: LessonEditorProps) => {
  const [activeView, setActiveView] = useState<'editor' | 'preview'>('editor');
  const [lessonTitle, setLessonTitle] = useState(lesson.title);
  const [lessonDescription, setLessonDescription] = useState(lesson.description || '');
  const [contentBlocks, setContentBlocks] = useState<ContentBlock[]>(
    lesson.contentBlocks || [
      {
        id: 'block-1',
        type: 'heading',
        order: 0,
        content: { text: 'Welcome to this lesson', level: 'h2' }
      },
      {
        id: 'block-2',
        type: 'text',
        order: 1,
        content: { text: 'This is where your lesson content will go. Click to edit this text and start building your lesson.' }
      }
    ]
  );

  const contentTools = [
    { type: 'heading', label: 'Heading', icon: <Heading className="w-4 h-4" />, description: 'Add section headings' },
    { type: 'text', label: 'Text', icon: <Type className="w-4 h-4" />, description: 'Rich text content' },
    { type: 'image', label: 'Image', icon: <Image className="w-4 h-4" />, description: 'Upload images' },
    { type: 'video', label: 'Video', icon: <Video className="w-4 h-4" />, description: 'Embed videos' },
    { type: 'audio', label: 'Audio', icon: <Mic className="w-4 h-4" />, description: 'Audio content' },
    { type: 'quiz', label: 'Quiz', icon: <HelpCircle className="w-4 h-4" />, description: 'Interactive questions' },
    { type: 'divider', label: 'Divider', icon: <Minus className="w-4 h-4" />, description: 'Section separator' },
    { type: 'button', label: 'Button', icon: <MousePointer className="w-4 h-4" />, description: 'Call-to-action button' },
    { type: 'code', label: 'Code', icon: <Code className="w-4 h-4" />, description: 'Code snippets' },
    { type: 'list', label: 'List', icon: <List className="w-4 h-4" />, description: 'Bulleted lists' },
    { type: 'quote', label: 'Quote', icon: <Quote className="w-4 h-4" />, description: 'Highlighted quotes' },
    { type: 'embed', label: 'Embed', icon: <Globe className="w-4 h-4" />, description: 'External content' },
  ];

  const addContentBlock = (type: string) => {
    const getDefaultContent = (blockType: string) => {
      switch (blockType) {
        case 'heading':
          return { text: '', level: 'h2' };
        case 'text':
          return { text: '' };
        case 'image':
          return { url: '', alt: '' };
        case 'video':
          return { url: '', title: '' };
        case 'quiz':
          return { question: '', options: ['', '', '', ''], correctAnswer: 0 };
        case 'list':
          return { items: [], listType: 'bullet' };
        case 'quote':
          return { text: '', author: '' };
        case 'button':
          return { text: '', url: '', style: 'primary', size: 'medium' };
        case 'code':
          return { code: '', language: 'javascript' };
        case 'divider':
          return { style: 'solid' };
        default:
          return {};
      }
    };

    const newBlock: ContentBlock = {
      id: `block-${Date.now()}`,
      type: type as ContentBlock['type'],
      order: contentBlocks.length,
      content: getDefaultContent(type),
    };
    setContentBlocks([...contentBlocks, newBlock]);
  };

  const moveContentBlock = (dragIndex: number, hoverIndex: number) => {
    const draggedBlock = contentBlocks[dragIndex];
    const newBlocks = [...contentBlocks];
    newBlocks.splice(dragIndex, 1);
    newBlocks.splice(hoverIndex, 0, draggedBlock);
    
    const updatedBlocks = newBlocks.map((block, index) => ({
      ...block,
      order: index
    }));
    
    setContentBlocks(updatedBlocks);
  };

  const updateContentBlock = (blockId: string, content: any) => {
    setContentBlocks(blocks => 
      blocks.map(block => 
        block.id === blockId ? { ...block, content } : block
      )
    );
  };

  const deleteContentBlock = (blockId: string) => {
    setContentBlocks(blocks => blocks.filter(block => block.id !== blockId));
  };

  const handleSave = () => {
    const updatedLesson: Lesson = {
      ...lesson,
      title: lessonTitle,
      description: lessonDescription,
      contentBlocks: contentBlocks,
      status: 'in-progress'
    };
    onSave(updatedLesson);
  };

  const getCurrentLesson = (): Lesson => ({
    ...lesson,
    title: lessonTitle,
    description: lessonDescription,
    contentBlocks: contentBlocks,
  });

  if (activeView === 'preview') {
    return (
      <LessonPreview
        lesson={getCurrentLesson()}
        moduleId={moduleId}
        onBack={() => setActiveView('editor')}
        onEdit={() => setActiveView('editor')}
      />
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
        {/* Header */}
        <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" onClick={onBack} className="p-2">
                  <ArrowLeft className="w-5 h-5" />
                </Button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {lessonTitle || "Lesson Editor"}
                  </h1>
                  <p className="text-sm text-gray-600">Edit lesson content and structure</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" onClick={() => setActiveView('preview')}>
                  <Eye className="w-4 h-4 mr-2" />
                  Preview
                </Button>
                <Button size="sm" onClick={handleSave} className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700">
                  <Save className="w-4 h-4 mr-2" />
                  Save Lesson
                </Button>
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Content Tools Sidebar */}
            <div className="lg:col-span-1">
              <Card className="sticky top-24">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Plus className="w-4 h-4" />
                    <span>Content Blocks</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[600px] pr-4">
                    <div className="space-y-2">
                      {contentTools.map((tool) => (
                        <ContentTool
                          key={tool.type}
                          type={tool.type}
                          label={tool.label}
                          icon={tool.icon}
                          description={tool.description}
                        />
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>

            {/* Main Content Area */}
            <div className="lg:col-span-3">
              <div className="space-y-6">
                {/* Lesson Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle>Lesson Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="lesson-title">Lesson Title</Label>
                      <Input
                        id="lesson-title"
                        value={lessonTitle}
                        onChange={(e) => setLessonTitle(e.target.value)}
                        placeholder="Enter lesson title..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="lesson-description">Description (Optional)</Label>
                      <Textarea
                        id="lesson-description"
                        value={lessonDescription}
                        onChange={(e) => setLessonDescription(e.target.value)}
                        placeholder="Brief description of this lesson..."
                        rows={2}
                      />
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge variant="outline">{lesson.type}</Badge>
                      <Badge variant="outline">{lesson.duration}</Badge>
                      <Badge variant="outline" className={
                        lesson.status === 'completed' ? 'bg-green-100 text-green-800' :
                        lesson.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }>
                        {lesson.status}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* Content Blocks */}
                <Card>
                  <CardHeader>
                    <CardTitle>Lesson Content</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <DropZone onDrop={(item) => addContentBlock(item.contentType)}>
                      <div className="p-4">
                        {contentBlocks.length === 0 ? (
                          <div className="text-center py-12">
                            <Plus className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">Start Building Your Lesson</h3>
                            <p className="text-gray-600 mb-4">Drag content blocks from the sidebar to begin</p>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {contentBlocks
                              .sort((a, b) => a.order - b.order)
                              .map((block, index) => (
                                <ContentBlockComponent
                                  key={block.id}
                                  block={block}
                                  index={index}
                                  onMove={moveContentBlock}
                                  onUpdate={updateContentBlock}
                                  onDelete={deleteContentBlock}
                                />
                              ))}
                            
                            <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 text-center">
                              <p className="text-sm text-gray-500">Drag more content blocks here to continue building your lesson</p>
                            </div>
                          </div>
                        )}
                      </div>
                    </DropZone>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

export default LessonEditor;
