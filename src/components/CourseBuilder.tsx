
import { useState } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Plus, Save, Eye, Share, FileText, HelpCircle, Download } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "@/components/ui/sonner";
import ModuleEditor from "@/components/ModuleEditor";
import AssessmentBuilder from "@/components/AssessmentBuilder";
import PreviewPanel from "@/components/PreviewPanel";
import LessonEditor from "@/components/LessonEditor";
import DraggableModule from "@/components/DraggableModule";
import DraggableLesson from "@/components/DraggableLesson";
import DropZone from "@/components/DropZone";
import DraggableContentTool from "@/components/DraggableContentTool";
import { Course, Module, Lesson, DragItem } from "@/types/course";
import { courseStorage } from "@/services/courseStorage";
import { downloadSCORMPackage } from "@/services/scormExport";

interface CourseBuilderProps {
  course?: Course;
  onBack: () => void;
}

const CourseBuilder = ({ course, onBack }: CourseBuilderProps) => {
  // Debug the incoming course data
  console.log('=== COURSEBUILDER RECEIVED COURSE ===');
  console.log('Course:', course);
  console.log('Course modules count:', course?.modules?.length || 0);
  if (course?.modules) {
    course.modules.forEach((module, index) => {
      console.log(`Module ${index}: ${module.title} (${module.lessons?.length || 0} lessons)`);
    });
  }

  const [activeTab, setActiveTab] = useState("structure");
  const [selectedModule, setSelectedModule] = useState<Module | null>(null);
  const [selectedLesson, setSelectedLesson] = useState<{ lesson: Lesson; moduleId: string } | null>(null);
  const [courseTitle, setCourseTitle] = useState(course?.title || "");
  const [courseDescription, setCourseDescription] = useState(course?.description || "");
  const [isSaving, setIsSaving] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Initialize with sample data in the new structure
  console.log('=== COURSEBUILDER INITIALIZING COURSEDATA ===');
  console.log('Using course modules:', course?.modules ? 'YES' : 'NO');
  console.log('Modules count:', course?.modules?.length || 0);

  const [courseData, setCourseData] = useState<Course>({
    id: course?.id || `course-${Date.now()}`,
    title: course?.title || "",
    description: course?.description || "",

    modules: course?.modules || [
      {
        id: "module-1",
        title: "Getting Started",
        description: "Introduction and basic concepts",
        order: 0,
        status: "completed",
        estimatedDuration: "45 min",
        lessons: [
          {
            id: "lesson-1-1",
            title: "Welcome to the Course",
            type: "text",
            duration: "15 min",
            content: "Welcome to our comprehensive learning journey...",
            status: "completed",
            order: 0,
            contentBlocks: [
              {
                id: 'welcome-heading',
                type: 'heading',
                order: 0,
                content: { text: 'Welcome to Our Learning Journey', level: 'h1' }
              },
              {
                id: 'welcome-text',
                type: 'text',
                order: 1,
                content: { text: 'This comprehensive course will take you through all the essential concepts you need to master. Each lesson is carefully crafted to build upon the previous one, ensuring a smooth learning experience.' }
              },
              {
                id: 'welcome-video',
                type: 'video',
                order: 2,
                content: { url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ', title: 'Course Introduction Video' }
              }
            ]
          },
          {
            id: "lesson-1-2",
            title: "Course Overview",
            type: "video",
            duration: "20 min",
            content: "",
            status: "completed",
            order: 1,
            contentBlocks: [
              {
                id: 'overview-heading',
                type: 'heading',
                order: 0,
                content: { text: 'Course Overview', level: 'h2' }
              },
              {
                id: 'overview-text',
                type: 'text',
                order: 1,
                content: { text: 'In this section, we\'ll provide you with a comprehensive overview of what you can expect from this course.' }
              },
              {
                id: 'overview-list',
                type: 'list',
                order: 2,
                content: {
                  items: [
                    'Understanding the fundamentals',
                    'Hands-on practical exercises',
                    'Real-world applications',
                    'Assessment and certification'
                  ],
                  listType: 'bullet'
                }
              },
              {
                id: 'overview-quote',
                type: 'quote',
                order: 3,
                content: {
                  text: 'Learning is not about memorizing facts, but about understanding concepts and applying them creatively.',
                  author: 'Educational Philosophy'
                }
              }
            ]
          },
          {
            id: "lesson-1-3",
            title: "Setting Expectations",
            type: "text",
            duration: "10 min",
            content: "",
            status: "completed",
            order: 2
          }
        ]
      },
      {
        id: "module-2",
        title: "Core Concepts",
        description: "Deep dive into fundamental concepts",
        order: 1,
        status: "in-progress",
        estimatedDuration: "90 min",
        lessons: [
          {
            id: "lesson-2-1",
            title: "Fundamental Principles",
            type: "text",
            duration: "25 min",
            content: "",
            status: "in-progress",
            order: 0
          },
          {
            id: "lesson-2-2",
            title: "Interactive Demo",
            type: "interactive",
            duration: "30 min",
            content: "",
            status: "draft",
            order: 1
          },
          {
            id: "lesson-2-3",
            title: "Knowledge Check",
            type: "quiz",
            duration: "15 min",
            content: "",
            status: "draft",
            order: 2,
            contentBlocks: [
              {
                id: 'quiz-intro',
                type: 'heading',
                order: 0,
                content: { text: 'Knowledge Check', level: 'h2' }
              },
              {
                id: 'quiz-instructions',
                type: 'text',
                order: 1,
                content: { text: 'Test your understanding of the concepts covered in this module. Choose the best answer for each question.' }
              },
              {
                id: 'quiz-question-1',
                type: 'quiz',
                order: 2,
                content: {
                  question: 'What is the primary benefit of using a modular approach in course design?',
                  options: [
                    'It makes courses longer',
                    'It improves learning outcomes and flexibility',
                    'It reduces the need for assessments',
                    'It eliminates the need for instructors'
                  ],
                  correctAnswer: 1
                }
              },
              {
                id: 'quiz-divider',
                type: 'divider',
                order: 3,
                content: { style: 'solid' }
              },
              {
                id: 'quiz-question-2',
                type: 'quiz',
                order: 4,
                content: {
                  question: 'Which content type is most effective for demonstrating complex procedures?',
                  options: [
                    'Text only',
                    'Audio narration',
                    'Interactive video demonstrations',
                    'Static images'
                  ],
                  correctAnswer: 2
                }
              }
            ]
          }
        ]
      }
    ],
    metadata: {
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: "1.0.0",
      author: "Course Author",
      tags: [],
      difficulty: "beginner",
      estimatedDuration: "2 hours",
      language: "en"
    },
    settings: {
      visibility: "private",
      pricing: "free",
      completionRequirements: "all-modules",
      adaptiveLearning: "disabled"
    }
  });



  const addModule = (type: string) => {
    const newModule: Module = {
      id: `module-${Date.now()}`,
      title: `New ${type} Module`,
      description: `A new ${type} module`,
      order: courseData.modules.length,
      status: "draft",
      estimatedDuration: "30 min",
      lessons: []
    };
 console.log(courseData)
    setCourseData(prev => ({
      ...prev,
      modules: [...prev.modules, newModule],
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString()
      }
    }));
  };

  const addLesson = (moduleId: string, type: string) => {
    const targetModule = courseData.modules.find(m => m.id === moduleId);
    if (!targetModule) return;

    const newLesson: Lesson = {
      id: `lesson-${Date.now()}`,
      title: `New ${type} Lesson`,
      type: type as Lesson['type'],
      duration: "15 min",
      content: "",
      status: "draft",
      order: targetModule.lessons.length
    };

    setCourseData(prev => ({
      ...prev,
      modules: prev.modules.map(module =>
        module.id === moduleId
          ? { ...module, lessons: [...module.lessons, newLesson] }
          : module
      ),
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString()
      }
    }));
  };

  const handleModuleClick = (module: Module) => {
    setSelectedModule(module);
    setActiveTab("editor");
  };

  const handleLessonClick = (lesson: Lesson, moduleId: string) => {
    setSelectedLesson({ lesson, moduleId });
    setActiveTab("lesson-editor");
  };

  // Drag and drop handlers
  const moveModule = (dragIndex: number, hoverIndex: number) => {
    const draggedModule = courseData.modules[dragIndex];
    const newModules = [...courseData.modules];
    newModules.splice(dragIndex, 1);
    newModules.splice(hoverIndex, 0, draggedModule);

    // Update order property
    const updatedModules = newModules.map((module, index) => ({
      ...module,
      order: index
    }));

    setCourseData(prev => ({
      ...prev,
      modules: updatedModules,
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString()
      }
    }));
  };

  const moveLesson = (dragIndex: number, hoverIndex: number, moduleId: string) => {
    setCourseData(prev => ({
      ...prev,
      modules: prev.modules.map(module => {
        if (module.id === moduleId) {
          const draggedLesson = module.lessons[dragIndex];
          const newLessons = [...module.lessons];
          newLessons.splice(dragIndex, 1);
          newLessons.splice(hoverIndex, 0, draggedLesson);

          // Update order property
          const updatedLessons = newLessons.map((lesson, index) => ({
            ...lesson,
            order: index
          }));

          return {
            ...module,
            lessons: updatedLessons
          };
        }
        return module;
      }),
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString()
      }
    }));
  };

  const handleAddLesson = (moduleId: string) => {
    addLesson(moduleId, 'text');
  };

  const handleDeleteModule = (moduleId: string) => {
    setCourseData(prev => ({
      ...prev,
      modules: prev.modules.filter(m => m.id !== moduleId),
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString()
      }
    }));
  };

  const handleDeleteLesson = (lessonId: string, moduleId: string) => {
    setCourseData(prev => ({
      ...prev,
      modules: prev.modules.map(module =>
        module.id === moduleId
          ? { ...module, lessons: module.lessons.filter(l => l.id !== lessonId) }
          : module
      ),
      metadata: {
        ...prev.metadata,
        updatedAt: new Date().toISOString()
      }
    }));
  };

  const handleContentToolDrop = (item: { type: string; contentType: string; label: string }, moduleId: string) => {
    if (item.type === 'content-tool') {
      const newLesson: Lesson = {
        id: `lesson-${Date.now()}`,
        title: `New ${item.label}`,
        type: item.contentType,
        duration: "15 min",
        content: "",
        status: "draft",
        order: courseData.modules.find(m => m.id === moduleId)?.lessons.length || 0
      };

      setCourseData(prev => ({
        ...prev,
        modules: prev.modules.map(module =>
          module.id === moduleId
            ? { ...module, lessons: [...module.lessons, newLesson] }
            : module
        ),
        metadata: {
          ...prev.metadata,
          updatedAt: new Date().toISOString()
        }
      }));
    }
  };

  // Save draft functionality
  const handleSaveDraft = async () => {
    setIsSaving(true);
    try {
      // Update course data with current title and description
      const updatedCourse: Course = {
        ...courseData,
        title: courseTitle,
        description: courseDescription,
        metadata: {
          ...courseData.metadata,
          updatedAt: new Date().toISOString()
        }
      };

      await courseStorage.saveDraft(updatedCourse);
      toast.success("Course saved as draft successfully!");
    } catch (error) {
      console.error('Error saving draft:', error);
      toast.error("Failed to save draft. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // SCORM export functionality
  const handleSCORMExport = async () => {
    setIsExporting(true);
    try {
      // Update course data with current title and description
      const updatedCourse: Course = {
        ...courseData,
        title: courseTitle,
        description: courseDescription,
        metadata: {
          ...courseData.metadata,
          updatedAt: new Date().toISOString()
        }
      };

      await downloadSCORMPackage(updatedCourse);
      toast.success("SCORM package downloaded successfully!");
    } catch (error) {
      console.error('Error exporting SCORM:', error);
      toast.error("Failed to export SCORM package. Please try again.");
    } finally {
      setIsExporting(false);
    }
  };

  // Publish course functionality
  const handlePublishCourse = async () => {
    try {
      // First save as draft to ensure it's in storage
      const updatedCourse: Course = {
        ...courseData,
        title: courseTitle,
        description: courseDescription,
        metadata: {
          ...courseData.metadata,
          updatedAt: new Date().toISOString()
        }
      };

      await courseStorage.saveDraft(updatedCourse);
      await courseStorage.publishCourse(updatedCourse.id);
      toast.success("Course published successfully!");
    } catch (error) {
      console.error('Error publishing course:', error);
      toast.error("Failed to publish course. Please try again.");
    }
  };

  if (activeTab === "lesson-editor" && selectedLesson) {
    return (
      <LessonEditor
        lesson={selectedLesson.lesson}
        moduleId={selectedLesson.moduleId}
        onBack={() => {
          setActiveTab("structure");
          setSelectedLesson(null);
        }}
        onSave={(updatedLesson) => {
          setCourseData(prev => ({
            ...prev,
            modules: prev.modules.map(module =>
              module.id === selectedLesson.moduleId
                ? {
                    ...module,
                    lessons: module.lessons.map(lesson =>
                      lesson.id === updatedLesson.id ? updatedLesson : lesson
                    )
                  }
                : module
            ),
            metadata: {
              ...prev.metadata,
              updatedAt: new Date().toISOString()
            }
          }));
          setActiveTab("structure");
          setSelectedLesson(null);
        }}
      />
    );
  }

  if (activeTab === "editor" && selectedModule) {
    return (
      <ModuleEditor
        module={selectedModule}
        onBack={() => {
          setActiveTab("structure");
          setSelectedModule(null);
        }}
        onSave={(updatedModule) => {
          setCourseData(prev => ({
            ...prev,
            modules: prev.modules.map(m => m.id === updatedModule.id ? updatedModule : m),
            metadata: {
              ...prev.metadata,
              updatedAt: new Date().toISOString()
            }
          }));
          setActiveTab("structure");
          setSelectedModule(null);
        }}
      />
    );
  }

  if (activeTab === "assessment") {
    return (
      <AssessmentBuilder 
        onBack={() => setActiveTab("structure")}
      />
    );
  }

  if (activeTab === "preview") {
    return (
      <PreviewPanel
        course={courseData}
        onBack={() => setActiveTab("structure")}
      />
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={onBack} className="p-2">
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  {courseTitle || "New Course"}
                </h1>
                <p className="text-sm text-gray-600">Course Builder</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSaveDraft}
                disabled={isSaving}
              >
                <Save className="w-4 h-4 mr-2" />
                {isSaving ? "Saving..." : "Save Draft"}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSCORMExport}
                disabled={isExporting}
              >
                <Download className="w-4 h-4 mr-2" />
                {isExporting ? "Exporting..." : "Download SCORM"}
              </Button>
              <Button
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700"
                onClick={handlePublishCourse}
              >
                Publish Course
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:w-96">
            <TabsTrigger value="structure">Structure</TabsTrigger>
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="assessment">Assessment</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="structure" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Course Info */}
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Course Information</CardTitle>
                    <CardDescription>Basic details about your course</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="title">Course Title</Label>
                      <Input 
                        id="title"
                        value={courseTitle}
                        onChange={(e) => setCourseTitle(e.target.value)}
                        placeholder="Enter course title..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Textarea 
                        id="description"
                        value={courseDescription}
                        onChange={(e) => setCourseDescription(e.target.value)}
                        placeholder="Describe your course..."
                        rows={4}
                      />
                    </div>
                    <Separator />
                    <div className="space-y-2">
                      <h4 className="font-medium">Course Progress</h4>
                      <Progress value={45} className="h-2" />
                      <p className="text-sm text-gray-600">4 of 8 modules completed</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Content Tools */}
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Content Tools</CardTitle>
                    <CardDescription>Drag to add content elements</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <DraggableContentTool
                        type="video"
                        label="Video Lesson"
                        description="Add video content with player controls"
                      />
                      <DraggableContentTool
                        type="text"
                        label="Text Content"
                        description="Rich text editor for written content"
                      />
                      <DraggableContentTool
                        type="quiz"
                        label="Quiz/Assessment"
                        description="Interactive questions and assessments"
                      />
                      <DraggableContentTool
                        type="image"
                        label="Image Gallery"
                        description="Visual content and image galleries"
                      />
                      <DraggableContentTool
                        type="audio"
                        label="Audio Content"
                        description="Podcasts, music, and audio lessons"
                      />
                      <DraggableContentTool
                        type="interactive"
                        label="Interactive"
                        description="Code examples, simulations, and activities"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Course Structure */}
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                      <CardTitle>Course Structure</CardTitle>
                      <CardDescription>Organize your learning modules</CardDescription>
                    </div>
                    <Button size="sm" onClick={() => addModule("text")}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Module
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-[600px] pr-4">
                      <div className="space-y-3">
                        {courseData.modules.map((module: Module, index: number) => (
                          <div key={module.id} className="space-y-2">
                            <DraggableModule
                              module={module}
                              index={index}
                              onMove={moveModule}
                              onClick={handleModuleClick}
                              onAddLesson={handleAddLesson}
                              onDelete={handleDeleteModule}
                            />

                            {/* Render lessons for this module */}
                            <div className="space-y-1">
                              {module.lessons.map((lesson: Lesson, lessonIndex: number) => (
                                <DraggableLesson
                                  key={lesson.id}
                                  lesson={lesson}
                                  moduleId={module.id}
                                  index={lessonIndex}
                                  onMove={moveLesson}
                                  onClick={(lesson) => {
                                    handleLessonClick(lesson, module.id);
                                  }}
                                  onDelete={handleDeleteLesson}
                                />
                              ))}

                              {/* Drop zone for adding lessons to this module */}
                              <DropZone
                                accept={['lesson', 'content-tool']}
                                moduleId={module.id}
                                onDrop={(item) => {
                                  if (item.type === 'content-tool') {
                                    handleContentToolDrop(item, module.id);
                                  } else {
                                    console.log('Lesson dropped:', item);
                                  }
                                }}
                                className="ml-6 mt-2 p-2 border border-dashed border-gray-300 rounded-md"
                              >
                                <div className="text-xs text-gray-500 text-center py-1">
                                  {module.lessons.length === 0
                                    ? "No lessons yet. Drag content tools here or click 'Add Lesson' above."
                                    : "Drag content tools here to add new lessons"
                                  }
                                </div>
                              </DropZone>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="content" className="space-y-6">
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Content Library</h3>
              <p className="text-gray-600 mb-4">Manage your course assets and content library</p>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Upload Content
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="assessment" className="space-y-6">
            <div className="text-center py-12">
              <HelpCircle className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Assessment Builder</h3>
              <p className="text-gray-600 mb-4">Create quizzes, tests, and interactive assessments</p>
              <Button onClick={() => setActiveTab("assessment")}>
                <Plus className="w-4 h-4 mr-2" />
                Create Assessment
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Publishing Settings</CardTitle>
                  <CardDescription>Control how your course is published and accessed</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="visibility">Visibility</Label>
                    <select className="w-full mt-1 p-2 border rounded-md">
                      <option>Private</option>
                      <option>Public</option>
                      <option>Organization Only</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="pricing">Pricing Model</Label>
                    <select className="w-full mt-1 p-2 border rounded-md">
                      <option>Free</option>
                      <option>Paid</option>
                      <option>Subscription</option>
                    </select>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Learning Settings</CardTitle>
                  <CardDescription>Configure learning paths and completion rules</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="completion">Completion Requirements</Label>
                    <select className="w-full mt-1 p-2 border rounded-md">
                      <option>Complete All Modules</option>
                      <option>Pass All Assessments</option>
                      <option>Custom Rules</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="adaptive">Adaptive Learning</Label>
                    <select className="w-full mt-1 p-2 border rounded-md">
                      <option>Disabled</option>
                      <option>Basic Adaptation</option>
                      <option>Advanced AI-Driven</option>
                    </select>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
    </DndProvider>
  );
};

export default CourseBuilder;
