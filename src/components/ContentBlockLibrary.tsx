import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Image, 
  Video, 
  Mic, 
  HelpCircle, 
  Minus, 
  MousePointer, 
  Code, 
  List, 
  Quote,
  Globe,
  Play,
  Upload,
  Link,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Bold,
  Italic,
  Underline
} from "lucide-react";
import { ContentBlock } from "@/types/course";

interface ContentBlockRendererProps {
  block: ContentBlock;
  isEditing: boolean;
  tempContent: any;
  onContentChange: (content: any) => void;
  onSave: () => void;
  onCancel: () => void;
}

export const ContentBlockRenderer = ({ 
  block, 
  isEditing, 
  tempContent, 
  onContentChange, 
  onSave, 
  onCancel 
}: ContentBlockRendererProps) => {
  const renderEditMode = () => {
    switch (block.type) {
      case 'heading':
        return (
          <div className="space-y-2">
            <Input
              value={tempContent.text || ''}
              onChange={(e) => onContentChange({ ...tempContent, text: e.target.value })}
              placeholder="Enter heading text..."
              className="text-lg font-semibold"
            />
            <div className="flex items-center space-x-2">
              <select 
                value={tempContent.level || 'h2'}
                onChange={(e) => onContentChange({ ...tempContent, level: e.target.value })}
                className="px-2 py-1 border rounded text-sm"
              >
                <option value="h1">H1</option>
                <option value="h2">H2</option>
                <option value="h3">H3</option>
                <option value="h4">H4</option>
              </select>
              <div className="flex items-center space-x-1">
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <AlignLeft className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <AlignCenter className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <AlignRight className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        );
      
      case 'text':
        return (
          <div className="space-y-2">
            <Textarea
              value={tempContent.text || ''}
              onChange={(e) => onContentChange({ ...tempContent, text: e.target.value })}
              placeholder="Enter your text content..."
              rows={6}
              className="resize-none"
            />
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1">
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <Bold className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <Italic className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <Underline className="h-3 w-3" />
                </Button>
              </div>
              <Separator orientation="vertical" className="h-4" />
              <div className="flex items-center space-x-1">
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <AlignLeft className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <AlignCenter className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" className="h-6 px-2">
                  <AlignRight className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        );
      
      case 'image':
        return (
          <div className="space-y-3">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {tempContent.url ? (
                <img 
                  src={tempContent.url} 
                  alt={tempContent.alt || ''} 
                  className="max-w-full h-auto mx-auto rounded"
                />
              ) : (
                <>
                  <Image className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500 mb-2">Upload an image or enter URL</p>
                  <Button size="sm" variant="outline">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Image
                  </Button>
                </>
              )}
            </div>
            <div className="space-y-2">
              <Input
                value={tempContent.url || ''}
                onChange={(e) => onContentChange({ ...tempContent, url: e.target.value })}
                placeholder="Image URL..."
              />
              <Input
                value={tempContent.alt || ''}
                onChange={(e) => onContentChange({ ...tempContent, alt: e.target.value })}
                placeholder="Alt text (for accessibility)..."
              />
            </div>
          </div>
        );
      
      case 'video':
        return (
          <div className="space-y-3">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              {tempContent.url ? (
                <div className="bg-gray-100 rounded-lg p-8">
                  <Video className="w-12 h-12 text-gray-600 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Video: {tempContent.title || 'Untitled'}</p>
                  <Button size="sm" variant="outline" className="mt-2">
                    <Play className="w-4 h-4 mr-2" />
                    Preview
                  </Button>
                </div>
              ) : (
                <>
                  <Video className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500 mb-2">Add video URL or upload</p>
                  <Button size="sm" variant="outline">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Video
                  </Button>
                </>
              )}
            </div>
            <div className="space-y-2">
              <Input
                value={tempContent.url || ''}
                onChange={(e) => onContentChange({ ...tempContent, url: e.target.value })}
                placeholder="Video URL (YouTube, Vimeo, etc.)..."
              />
              <Input
                value={tempContent.title || ''}
                onChange={(e) => onContentChange({ ...tempContent, title: e.target.value })}
                placeholder="Video title..."
              />
            </div>
          </div>
        );
      
      case 'quiz':
        return (
          <div className="space-y-3">
            <Input
              value={tempContent.question || ''}
              onChange={(e) => onContentChange({ ...tempContent, question: e.target.value })}
              placeholder="Enter your question..."
            />
            <div className="space-y-2">
              <label className="text-sm font-medium">Answer Options:</label>
              {(tempContent.options || ['', '', '', '']).map((option: string, index: number) => (
                <div key={index} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="correct-answer"
                    checked={tempContent.correctAnswer === index}
                    onChange={() => onContentChange({ 
                      ...tempContent, 
                      correctAnswer: index 
                    })}
                  />
                  <Input
                    value={option}
                    onChange={(e) => {
                      const newOptions = [...(tempContent.options || ['', '', '', ''])];
                      newOptions[index] = e.target.value;
                      onContentChange({ ...tempContent, options: newOptions });
                    }}
                    placeholder={`Option ${index + 1}...`}
                  />
                </div>
              ))}
            </div>
          </div>
        );
      
      case 'list':
        return (
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <select 
                value={tempContent.listType || 'bullet'}
                onChange={(e) => onContentChange({ ...tempContent, listType: e.target.value })}
                className="px-2 py-1 border rounded text-sm"
              >
                <option value="bullet">Bullet List</option>
                <option value="numbered">Numbered List</option>
              </select>
            </div>
            <Textarea
              value={tempContent.items?.join('\n') || ''}
              onChange={(e) => onContentChange({ 
                ...tempContent, 
                items: e.target.value.split('\n').filter(item => item.trim()) 
              })}
              placeholder="Enter list items (one per line)..."
              rows={5}
            />
          </div>
        );
      
      case 'quote':
        return (
          <div className="space-y-2">
            <Textarea
              value={tempContent.text || ''}
              onChange={(e) => onContentChange({ ...tempContent, text: e.target.value })}
              placeholder="Enter quote text..."
              rows={3}
            />
            <Input
              value={tempContent.author || ''}
              onChange={(e) => onContentChange({ ...tempContent, author: e.target.value })}
              placeholder="Quote author (optional)..."
            />
          </div>
        );
      
      case 'button':
        return (
          <div className="space-y-2">
            <Input
              value={tempContent.text || ''}
              onChange={(e) => onContentChange({ ...tempContent, text: e.target.value })}
              placeholder="Button text..."
            />
            <Input
              value={tempContent.url || ''}
              onChange={(e) => onContentChange({ ...tempContent, url: e.target.value })}
              placeholder="Button URL..."
            />
            <div className="flex items-center space-x-2">
              <select 
                value={tempContent.style || 'primary'}
                onChange={(e) => onContentChange({ ...tempContent, style: e.target.value })}
                className="px-2 py-1 border rounded text-sm"
              >
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="outline">Outline</option>
              </select>
              <select 
                value={tempContent.size || 'medium'}
                onChange={(e) => onContentChange({ ...tempContent, size: e.target.value })}
                className="px-2 py-1 border rounded text-sm"
              >
                <option value="small">Small</option>
                <option value="medium">Medium</option>
                <option value="large">Large</option>
              </select>
            </div>
          </div>
        );
      
      case 'code':
        return (
          <div className="space-y-2">
            <select 
              value={tempContent.language || 'javascript'}
              onChange={(e) => onContentChange({ ...tempContent, language: e.target.value })}
              className="px-2 py-1 border rounded text-sm"
            >
              <option value="javascript">JavaScript</option>
              <option value="python">Python</option>
              <option value="html">HTML</option>
              <option value="css">CSS</option>
              <option value="json">JSON</option>
              <option value="sql">SQL</option>
            </select>
            <Textarea
              value={tempContent.code || ''}
              onChange={(e) => onContentChange({ ...tempContent, code: e.target.value })}
              placeholder="Enter your code..."
              rows={8}
              className="font-mono text-sm"
            />
          </div>
        );
      
      case 'divider':
        return (
          <div className="space-y-2">
            <select 
              value={tempContent.style || 'solid'}
              onChange={(e) => onContentChange({ ...tempContent, style: e.target.value })}
              className="px-2 py-1 border rounded text-sm"
            >
              <option value="solid">Solid Line</option>
              <option value="dashed">Dashed Line</option>
              <option value="dotted">Dotted Line</option>
              <option value="space">Space Only</option>
            </select>
          </div>
        );
      
      default:
        return (
          <Textarea
            value={tempContent.text || ''}
            onChange={(e) => onContentChange({ ...tempContent, text: e.target.value })}
            placeholder="Enter content..."
            rows={4}
          />
        );
    }
  };

  const renderViewMode = () => {
    switch (block.type) {
      case 'heading':
        const HeadingTag = (block.content?.level || 'h2') as keyof JSX.IntrinsicElements;
        return (
          <HeadingTag className="font-semibold text-gray-900">
            {block.content?.text || 'Click to edit heading'}
          </HeadingTag>
        );
      
      case 'text':
        return (
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-700 leading-relaxed">
              {block.content?.text || 'Click to add text content'}
            </p>
          </div>
        );
      
      case 'image':
        return block.content?.url ? (
          <div className="text-center">
            <img 
              src={block.content.url} 
              alt={block.content.alt || ''} 
              className="max-w-full h-auto mx-auto rounded-lg shadow-sm"
            />
            {block.content.alt && (
              <p className="text-sm text-gray-500 mt-2">{block.content.alt}</p>
            )}
          </div>
        ) : (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <Image className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">Click to add image</p>
          </div>
        );
      
      case 'video':
        return block.content?.url ? (
          <div className="bg-gray-100 rounded-lg p-8 text-center">
            <Video className="w-12 h-12 text-gray-600 mx-auto mb-2" />
            <p className="text-sm text-gray-600 mb-2">
              {block.content.title || 'Video Content'}
            </p>
            <Button size="sm" variant="outline">
              <Play className="w-4 h-4 mr-2" />
              Play Video
            </Button>
          </div>
        ) : (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <Video className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">Click to add video</p>
          </div>
        );
      
      case 'quiz':
        return (
          <Card className="bg-blue-50 border-blue-200">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2 mb-3">
                <HelpCircle className="w-5 h-5 text-blue-600" />
                <Badge variant="outline" className="bg-blue-100 text-blue-800">Quiz</Badge>
              </div>
              <h4 className="font-medium mb-3">
                {block.content?.question || 'Click to add question'}
              </h4>
              {block.content?.options?.map((option: string, index: number) => (
                <div key={index} className="flex items-center space-x-2 mb-2">
                  <div className="w-4 h-4 border border-gray-300 rounded-full"></div>
                  <span className="text-sm">{option || `Option ${index + 1}`}</span>
                </div>
              ))}
            </CardContent>
          </Card>
        );
      
      case 'list':
        const items = block.content?.items || [];
        return (
          <div className="space-y-1">
            {items.map((item: string, index: number) => (
              <div key={index} className="flex items-start space-x-2">
                {block.content?.listType === 'numbered' ? (
                  <span className="text-sm font-medium text-gray-600 mt-0.5">
                    {index + 1}.
                  </span>
                ) : (
                  <span className="text-gray-600 mt-1.5">•</span>
                )}
                <span className="text-gray-700">{item}</span>
              </div>
            ))}
            {items.length === 0 && (
              <p className="text-gray-500 text-sm">Click to add list items</p>
            )}
          </div>
        );
      
      case 'quote':
        return (
          <blockquote className="border-l-4 border-blue-500 pl-4 py-2 bg-gray-50 rounded-r">
            <p className="text-gray-700 italic">
              "{block.content?.text || 'Click to add quote'}"
            </p>
            {block.content?.author && (
              <cite className="text-sm text-gray-600 mt-2 block">
                — {block.content.author}
              </cite>
            )}
          </blockquote>
        );
      
      case 'button':
        const buttonStyle = block.content?.style || 'primary';
        const buttonSize = block.content?.size || 'medium';
        return (
          <div className="text-center">
            <Button 
              variant={buttonStyle === 'primary' ? 'default' : buttonStyle === 'outline' ? 'outline' : 'secondary'}
              size={buttonSize === 'small' ? 'sm' : buttonSize === 'large' ? 'lg' : 'default'}
            >
              {block.content?.text || 'Button Text'}
            </Button>
          </div>
        );
      
      case 'code':
        return (
          <div className="bg-gray-900 text-gray-100 rounded-lg p-4 overflow-x-auto">
            <div className="flex items-center justify-between mb-2">
              <Badge variant="outline" className="bg-gray-800 text-gray-300">
                {block.content?.language || 'code'}
              </Badge>
            </div>
            <pre className="text-sm">
              <code>{block.content?.code || '// Click to add code'}</code>
            </pre>
          </div>
        );
      
      case 'divider':
        const style = block.content?.style || 'solid';
        return (
          <div className="py-4">
            {style === 'space' ? (
              <div className="h-4"></div>
            ) : (
              <hr className={`border-gray-300 ${
                style === 'dashed' ? 'border-dashed' : 
                style === 'dotted' ? 'border-dotted' : ''
              }`} />
            )}
          </div>
        );
      
      default:
        return <p className="text-gray-500">Content block: {block.type}</p>;
    }
  };

  return (
    <div className="w-full">
      {isEditing ? (
        <div className="space-y-3">
          {renderEditMode()}
          <div className="flex items-center space-x-2 pt-2">
            <Button size="sm" onClick={onSave}>
              Save
            </Button>
            <Button size="sm" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        renderViewMode()
      )}
    </div>
  );
};
