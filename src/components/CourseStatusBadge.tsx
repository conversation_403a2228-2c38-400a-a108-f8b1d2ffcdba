import { Badge } from "@/components/ui/badge";
import { getStatusColor, getStatusIcon } from "@/services/courseStorage";
import type { StoredCourse } from "@/services/courseStorage";

interface CourseStatusBadgeProps {
  status: StoredCourse['status'];
  className?: string;
}

export const CourseStatusBadge = ({ status, className = "" }: CourseStatusBadgeProps) => {
  const statusColor = getStatusColor(status);
  const statusIcon = getStatusIcon(status);
  
  const statusText = {
    'draft': 'Draft',
    'in-progress': 'In Progress',
    'published': 'Published'
  }[status];

  return (
    <Badge className={`${statusColor} ${className}`}>
      <span className="mr-1">{statusIcon}</span>
      {statusText}
    </Badge>
  );
};

export default CourseStatusBadge;
