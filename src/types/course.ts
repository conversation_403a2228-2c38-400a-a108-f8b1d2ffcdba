export interface ContentBlock {
  id: string;
  type: 'text' | 'heading' | 'image' | 'video' | 'audio' | 'quiz' | 'divider' | 'button' | 'code' | 'list' | 'quote' | 'embed';
  order: number;
  content: any;
  settings?: {
    alignment?: 'left' | 'center' | 'right';
    size?: 'small' | 'medium' | 'large';
    style?: string;
    backgroundColor?: string;
    textColor?: string;
  };
}

export interface Lesson {
  id: string;
  title: string;
  type: 'text' | 'video' | 'audio' | 'image' | 'quiz' | 'interactive';
  duration: string;
  content: string;
  contentBlocks?: ContentBlock[];
  status: 'draft' | 'in-progress' | 'completed';
  order: number;
  description?: string;
  metadata?: {
    videoUrl?: string;
    audioUrl?: string;
    imageUrls?: string[];
    quizData?: any;
    interactiveData?: any;
  };
}

export interface Module {
  id: string;
  title: string;
  description: string;
  order: number;
  lessons: Lesson[];
  status: 'draft' | 'in-progress' | 'completed';
  estimatedDuration: string;
  learningObjectives?: string[];
}

export interface Course {
  id: string;
  title: string;
  description: string;
  modules: Module[];
  metadata: {
    createdAt: string;
    updatedAt: string;
    version: string;
    author: string;
    tags: string[];
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    estimatedDuration: string;
    language: string;
  };
  settings: {
    visibility: 'private' | 'public' | 'organization';
    pricing: 'free' | 'paid' | 'subscription';
    completionRequirements: 'all-modules' | 'pass-assessments' | 'custom';
    adaptiveLearning: 'disabled' | 'basic' | 'advanced';
  };
}

export interface DragItem {
  type: 'module' | 'lesson' | 'content-block' | 'content-tool';
  id: string;
  index: number;
  moduleId?: string; // For lessons, which module they belong to
  contentType?: string; // For content tools
  label?: string; // For content tools
}

export interface DropResult {
  type: 'module' | 'lesson';
  targetIndex: number;
  targetModuleId?: string; // For lessons, which module to drop into
}
