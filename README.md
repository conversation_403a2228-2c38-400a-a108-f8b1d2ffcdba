# EduAuthor Pro - Advanced E-Learning Authoring Platform

A comprehensive course building platform with advanced drag-and-drop lesson editing capabilities, inspired by modern e-learning authoring tools like Articulate Rise.

## 🚀 Key Features

### Lesson Editor
- **Full drag-and-drop interface** for content blocks within lessons
- **12+ content block types**: Headings, Text, Images, Videos, Quizzes, Lists, Quotes, Buttons, Code blocks, Dividers, Audio, and Embeds
- **Interactive content editing** with inline editing capabilities
- **Real-time preview** functionality
- **Content persistence** with automatic saving
- **Responsive design** that works on all devices

### Course Builder
- **Modular course structure** with drag-and-drop module and lesson organization
- **Content tools sidebar** for easy content creation
- **Progress tracking** and status management
- **Collaborative features** for team-based course development

## Project info

**URL**: https://lovable.dev/projects/c19af7cb-2255-46a6-ac0a-ae769a39c723

## 🎯 How to Use the Lesson Editor

### Accessing the Lesson Editor
1. Navigate to the main dashboard
2. Click "Create New Course" or select an existing course
3. In the Course Builder, click on any lesson's "Edit" button
4. The dedicated Lesson Editor will open

### Using the Lesson Editor
1. **Content Blocks Sidebar**: Drag content blocks from the left sidebar into your lesson
2. **Inline Editing**: Click on any content block to edit it directly
3. **Drag to Reorder**: Use the grip handle to drag and reorder content blocks
4. **Preview Mode**: Click "Preview" to see how your lesson will look to learners
5. **Save Changes**: Click "Save Lesson" to persist your changes

### Available Content Blocks
- **Heading**: Section titles with customizable levels (H1-H4)
- **Text**: Rich text content with formatting options
- **Image**: Upload images or use URLs with alt text support
- **Video**: Embed videos from YouTube, Vimeo, or upload directly
- **Audio**: Add audio content and podcasts
- **Quiz**: Interactive questions with multiple choice answers
- **List**: Bulleted or numbered lists
- **Quote**: Highlighted quotes with author attribution
- **Button**: Call-to-action buttons with custom styling
- **Code**: Syntax-highlighted code blocks with language selection
- **Divider**: Section separators with different styles
- **Embed**: External content integration

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/c19af7cb-2255-46a6-ac0a-ae769a39c723) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/c19af7cb-2255-46a6-ac0a-ae769a39c723) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
